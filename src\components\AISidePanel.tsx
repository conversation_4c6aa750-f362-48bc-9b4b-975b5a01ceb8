import React, { useState, useRef, useEffect } from 'react';
import { Bo<PERSON>, User, Send, Lightbulb, Zap, Loader2 } from 'lucide-react';
import { chatbotService } from '../services/chatbotService';
import { commandExecutor } from '../services/commandExecutor';
import type { ApiAnalysisResponse } from '../types';

interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    suggestions?: string[];
    actions?: string[];
  };
}

interface AISidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  currentAnalysisData: ApiAnalysisResponse | null;
  nodes: any[];
  onAddAIAgent: (agentType: string) => void;
  onNodeUpdate: (nodeId: string, updates: any) => void;
  onRefreshProject?: () => void;
}

const AISidePanel: React.FC<AISidePanelProps> = ({
  isOpen,
  onClose,
  currentAnalysisData,
  nodes,
  onAddAIAgent,
  onNodeUpdate,
  onRefreshProject
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when panel opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Generate contextual suggestions based on current state
  useEffect(() => {
    const generateSuggestions = () => {
      const newSuggestions: string[] = [];
      
      // Debug: Log current analysis data
      console.log('🔍 AISidePanel currentAnalysisData:', {
        hasData: !!currentAnalysisData,
        scenes: currentAnalysisData?.scenes?.length || 0,
        fileName: currentAnalysisData?.fileName
      });
      
      if (currentAnalysisData && currentAnalysisData.scenes && currentAnalysisData.scenes.length > 0) {
        const sceneCount = currentAnalysisData.scenes.length;
        newSuggestions.push(
          `Analyze all ${sceneCount} scenes for content`,
          'Add subtitle agents to all scenes',
          'Enhance video quality for the entire project'
        );
        
        if (sceneCount > 1) {
          newSuggestions.push('Create smooth transitions between scenes');
        }
      } else {
        newSuggestions.push(
          'Upload and analyze a new video',
          'Show me available AI agents',
          'Help me get started with video editing'
        );
      }
      
      setSuggestions(newSuggestions.slice(0, 3));
    };

    generateSuggestions();
  }, [currentAnalysisData, nodes]);

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isProcessing) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);

    try {
      // Parse the command using the chatbot service
      const parsedCommand = await chatbotService.parseCommand(message, {
        currentAnalysisData,
        nodes
      });

      // Execute the command
      const result = await commandExecutor.executeCommand(parsedCommand, {
        currentAnalysisData,
        nodes,
        onAddAIAgent,
        onNodeUpdate,
        onRefreshProject,
        onShowToast: (message: string, type: 'info' | 'error' | 'success' | 'warning') => {
          console.log(`Toast: ${type} - ${message}`);
        }
      });

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: result.response,
        timestamp: new Date(),
        metadata: {
          suggestions: result.suggestions,
          actions: result.actions
        }
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error('Error processing message:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'system',
        content: `Sorry, I encountered an error processing your request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(inputValue);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-96 flex flex-col" style={{
      backgroundColor: 'var(--bg-dark)',
      borderLeft: '1px solid var(--primary-dark)',
      zIndex: 1000
    }}>
      {/* Header */}
      <div className="flex items-center justify-between p-4" style={{
        borderBottom: '1px solid var(--primary-dark)',
        background: 'linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(157, 78, 221, 0.05) 100%)'
      }}>
        <div className="flex items-center gap-2">
          <Bot className="w-5 h-5" style={{ color: 'var(--primary-light)' }} />
          <h2 className="font-semibold" style={{ color: 'var(--text-light)' }}>AI Assistant</h2>
          <span className="text-xs opacity-60">
            ({currentAnalysisData?.scenes?.length || 0} scenes)
          </span>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => {
              console.log('🔄 Manual refresh triggered');
              onRefreshProject?.();
            }}
            className="p-1 rounded-md transition-colors text-xs"
            style={{
              color: 'var(--text-light)',
              opacity: 0.7
            }}
            title="Refresh project data"
          >
            🔄
          </button>
          <button
            onClick={onClose}
            className="p-1 rounded-md transition-colors"
            style={{
              color: 'var(--text-light)',
              opacity: 0.7
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-light)';
              e.currentTarget.style.opacity = '1';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.opacity = '0.7';
            }}
            title="Close AI Assistant"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.type !== 'user' && (
              <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style={{
                backgroundColor: message.type === 'system' ? 'var(--bg-light)' : 'var(--primary-dark)'
              }}>
                {message.type === 'system' ? (
                  <Lightbulb className="w-4 h-4" style={{ color: 'var(--warning)' }} />
                ) : (
                  <Bot className="w-4 h-4" style={{ color: 'var(--text-light)' }} />
                )}
              </div>
            )}

            <div className="max-w-[80%] rounded-lg p-3" style={{
              backgroundColor: message.type === 'user'
                ? 'var(--primary)'
                : message.type === 'system'
                ? 'var(--bg-light)'
                : 'var(--bg-light)',
              color: message.type === 'user'
                ? 'white'
                : 'var(--text-light)',
              border: message.type === 'system' ? '1px solid var(--primary-dark)' : 'none'
            }}>
              <div className="whitespace-pre-wrap text-sm">{message.content}</div>
              
              {message.metadata?.suggestions && (
                <div className="mt-2 pt-2" style={{ borderTop: '1px solid var(--primary-dark)' }}>
                  <div className="text-xs mb-1" style={{ color: 'var(--text-light)', opacity: 0.7 }}>Suggestions:</div>
                  {message.metadata.suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="block text-xs mb-1 hover:opacity-80 transition-opacity"
                      style={{ color: 'var(--primary-light)' }}
                    >
                      • {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {message.type === 'user' && (
              <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style={{
                backgroundColor: 'var(--primary)'
              }}>
                <User className="w-4 h-4 text-white" />
              </div>
            )}
          </div>
        ))}
        
        {isProcessing && (
          <div className="flex gap-3 justify-start">
            <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{
              backgroundColor: 'var(--primary-dark)'
            }}>
              <Loader2 className="w-4 h-4 animate-spin" style={{ color: 'var(--text-light)' }} />
            </div>
            <div className="rounded-lg p-3" style={{
              backgroundColor: 'var(--bg-light)'
            }}>
              <div className="text-sm" style={{ color: 'var(--text-light)', opacity: 0.8 }}>Processing your request...</div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <div className="p-4" style={{
          borderTop: '1px solid var(--primary-dark)',
          backgroundColor: 'var(--bg-light)'
        }}>
          <div className="text-xs mb-2 flex items-center gap-1" style={{
            color: 'var(--text-light)',
            opacity: 0.8
          }}>
            <Zap className="w-3 h-3" style={{ color: 'var(--warning)' }} />
            Quick suggestions:
          </div>
          <div className="space-y-1">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="block w-full text-left text-xs p-2 rounded transition-colors"
                style={{
                  color: 'var(--primary-light)',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--primary-dark)';
                  e.currentTarget.style.color = 'var(--text-light)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = 'var(--primary-light)';
                }}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4" style={{ borderTop: '1px solid var(--primary-dark)' }}>
        <div className="flex gap-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me to help with your video editing..."
            className="flex-1 px-3 py-2 rounded-md text-sm transition-colors"
            style={{
              backgroundColor: 'var(--bg-light)',
              color: 'var(--text-light)',
              border: '1px solid var(--primary-dark)',
              outline: 'none'
            }}
            onFocus={(e) => {
              e.currentTarget.style.borderColor = 'var(--primary-light)';
            }}
            onBlur={(e) => {
              e.currentTarget.style.borderColor = 'var(--primary-dark)';
            }}
            disabled={isProcessing}
          />
          <button
            onClick={() => handleSendMessage(inputValue)}
            disabled={!inputValue.trim() || isProcessing}
            className="px-3 py-2 rounded-md transition-colors"
            style={{
              backgroundColor: inputValue.trim() && !isProcessing ? 'var(--primary)' : 'var(--bg-light)',
              color: inputValue.trim() && !isProcessing ? 'white' : 'var(--text-light)',
              opacity: inputValue.trim() && !isProcessing ? 1 : 0.5,
              cursor: inputValue.trim() && !isProcessing ? 'pointer' : 'not-allowed'
            }}
            onMouseEnter={(e) => {
              if (inputValue.trim() && !isProcessing) {
                e.currentTarget.style.backgroundColor = 'var(--primary-light)';
              }
            }}
            onMouseLeave={(e) => {
              if (inputValue.trim() && !isProcessing) {
                e.currentTarget.style.backgroundColor = 'var(--primary)';
              }
            }}
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AISidePanel;
